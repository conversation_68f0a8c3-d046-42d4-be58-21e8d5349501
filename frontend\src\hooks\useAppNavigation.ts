import { useNavigate } from 'react-router-dom';
import { useContext } from 'react';
import { projectManagementAppContext } from '../App';
import { projectManagementAppContextType } from '../types';
import { Project, OpportunityTracking } from '../models';

export const useAppNavigation = () => {
  const navigate = useNavigate();
  const context = useContext(projectManagementAppContext) as projectManagementAppContextType;

  const navigateToHome = () => navigate('/');
  const navigateToDashboard = () => navigate('/dashboard');
  const navigateToLogin = () => navigate('/login');
  const navigateToBusinessDevelopment = () => navigate('/business-development');
  const navigateToProjectManagement = () => navigate('/project-management');
  const navigateToResources = () => navigate('/resources');
  const navigateToAdmin = () => navigate('/admin');

  const navigateToBusinessDevelopmentDetails = (project?: Project | OpportunityTracking) => {
    if (project && context?.setSelectedProject) {
      context.setSelectedProject(project);
    }
    navigate('/business-development/details');
  };

  const navigateToProjectDetails = (project?: Project | OpportunityTracking) => {
    if (project && context?.setSelectedProject) {
      context.setSelectedProject(project);
    }
    navigate('/project-management/details');
  };

  const navigateToGoNoGoForm = (project?: Project | OpportunityTracking) => {
    if (project && context?.setSelectedProject) {
      context.setSelectedProject(project);
    }
    navigate('/business-development/gonogo-form');
  };

  const navigateToBidPreparation = (project?: Project | OpportunityTracking) => {
    if (project && context?.setSelectedProject) {
      context.setSelectedProject(project);
    }
    navigate('/business-development/bid-preparation');
  };

  return {
    navigateToHome,
    navigateToDashboard,
    navigateToLogin,
    navigateToBusinessDevelopment,
    navigateToProjectManagement,
    navigateToResources,
    navigateToAdmin,
    navigateToBusinessDevelopmentDetails,
    navigateToProjectDetails,
    navigateToGoNoGoForm,
    navigateToBidPreparation,
  };
};
