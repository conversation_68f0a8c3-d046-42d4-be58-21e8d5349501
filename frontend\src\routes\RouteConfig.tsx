import React, { useContext } from 'react';
import { RouteObject, Outlet, useLocation } from 'react-router-dom';
import { Home, ProjectDetails, LoginScreen, BusinessDevelopment, ProjectManagement, BusinessDevelopmentDetails, AdminPanel } from '../pages';
import { Dashboard } from '../components/Dashboard';
import { ResourceManagement } from '../components/ResourceManagement';
import GoNoGoForm from '../components/forms/GoNoGoForm';
import BidPreparationForm from '../components/forms/BidPreparationForm';
import ProtectedRoute from './ProtectedRoute';
import { PermissionType } from '../models';
import { Navbar } from '../components/navigation/Navbar';
import { LoadingProvider } from '../context/LoadingContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { projectManagementAppContext } from '../App';
import { projectManagementAppContextType } from '../types';

// Layout component that includes Navbar
const Layout = () => {
  const location = useLocation();
  const context = useContext(projectManagementAppContext) as projectManagementAppContextType;

  // Don't show navbar on login page
  const showNavbar = location.pathname !== '/login' && context?.isAuthenticated;

  return (
    <LoadingProvider>
      <LoadingSpinner />
      {showNavbar && <Navbar />}
      <Outlet />
    </LoadingProvider>
  );
};

export const routes: RouteObject[] = [
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: 'login',
        element: <LoginScreen />,
      },
      {
        path: '',
        element: <ProtectedRoute />,
        children: [
          {
            index: true,
            element: <Home />,
          },
          {
            path: 'home',
            element: <Home />,
          },
          {
            path: 'dashboard',
            element: <Dashboard />,
          },
          {
            path: 'business-development',
            element: <ProtectedRoute requiredPermission={PermissionType.VIEW_BUSINESS_DEVELOPMENT} />,
            children: [
              {
                index: true,
                element: <BusinessDevelopment />,
              },
              {
                path: 'details',
                element: <BusinessDevelopmentDetails />,
              },
              {
                path: 'gonogo-form',
                element: <GoNoGoFormWrapper />,
              },
              {
                path: 'bid-preparation',
                element: <BidPreparationForm />,
              },
            ],
          },
          {
            path: 'project-management',
            element: <ProtectedRoute requiredPermission={PermissionType.VIEW_PROJECT} />,
            children: [
              {
                index: true,
                element: <ProjectManagement />,
              },
              {
                path: 'details',
                element: <ProjectDetails />,
              },
            ],
          },
          {
            path: 'resources',
            element: <ResourceManagement />,
          },
          {
            path: 'admin',
            element: <ProtectedRoute requiredPermission={PermissionType.SYSTEM_ADMIN} />,
            children: [
              {
                index: true,
                element: <AdminPanel />,
              },
            ],
          },
        ],
      },
    ],
  },
];
